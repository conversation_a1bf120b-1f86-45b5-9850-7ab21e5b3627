@echo off
chcp 65001 >nul
title uTools 管理工具集

:main_menu
cls
echo.
echo ╔══════════════════════════════════════╗
echo ║         uTools 管理工具集            ║
echo ╚══════════════════════════════════════╝
echo.
echo 请选择要使用的工具:
echo.
echo [1] 备份恢复工具 (utools_backup_restore.py)
echo [2] 插件名称映射工具 (plugin_name_mapper.py)
echo [3] 打开uTools数据目录
echo [4] 查看使用指南
echo [5] 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto backup_tool
if "%choice%"=="2" goto mapper_tool
if "%choice%"=="3" goto open_directory
if "%choice%"=="4" goto show_guide
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
pause
goto main_menu

:backup_tool
cls
echo.
echo ========================================
echo       启动备份恢复工具
echo ========================================
echo.

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python
    goto python_error
)

REM 检查文件
if not exist "utools_backup_restore.py" (
    echo [错误] 未找到 utools_backup_restore.py 文件
    pause
    goto main_menu
)

echo [信息] 正在启动备份恢复工具...
echo.
python utools_backup_restore.py
echo.
echo [完成] 工具已退出
pause
goto main_menu

:mapper_tool
cls
echo.
echo ========================================
echo      启动插件名称映射工具
echo ========================================
echo.

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python
    goto python_error
)

REM 检查文件
if not exist "plugin_name_mapper.py" (
    echo [错误] 未找到 plugin_name_mapper.py 文件
    pause
    goto main_menu
)

echo [信息] 正在启动插件名称映射工具...
echo.
python plugin_name_mapper.py
echo.
echo [完成] 工具已退出
pause
goto main_menu

:open_directory
cls
echo.
echo ========================================
echo       打开uTools数据目录
echo ========================================
echo.

set "utools_dir=%USERPROFILE%\AppData\Roaming\uTools"

if exist "%utools_dir%" (
    echo [信息] 正在打开目录: %utools_dir%
    explorer "%utools_dir%"
    echo [完成] 目录已在文件管理器中打开
) else (
    echo [错误] uTools数据目录不存在: %utools_dir%
    echo 请检查uTools是否已安装
)

echo.
pause
goto main_menu

:show_guide
cls
echo.
echo ========================================
echo         查看使用指南
echo ========================================
echo.

if exist "utools_plugin_guide.md" (
    echo [信息] 正在打开使用指南...
    start "" "utools_plugin_guide.md"
    echo [完成] 指南已在默认程序中打开
) else (
    echo [错误] 未找到使用指南文件: utools_plugin_guide.md
)

echo.
pause
goto main_menu

:python_error
echo.
echo Python环境问题:
echo 1. 请确保已安装Python 3.6+
echo 2. 下载地址: https://www.python.org/downloads/
echo 3. 安装时请勾选 "Add Python to PATH"
echo.
pause
goto main_menu

:exit
echo.
echo 感谢使用uTools管理工具集！
echo.
pause
exit /b 0
