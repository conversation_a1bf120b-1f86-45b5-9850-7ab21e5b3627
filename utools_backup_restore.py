#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uTools 完整备份与恢复工具
支持插件、用户配置、数据库的完整备份和恢复
"""

import json
import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime
import hashlib

class UToolsBackupRestore:
    def __init__(self, utools_path=None):
        if utools_path is None:
            self.utools_path = Path.home() / "AppData" / "Roaming" / "uTools"
        else:
            self.utools_path = Path(utools_path)
        
        # 关键目录和文件
        self.plugins_path = self.utools_path / "plugins"
        self.database_path = self.utools_path / "database"
        self.important_files = [
            "Preferences",      # 用户偏好设置
            "settings",         # 应用设置
            "Local State",      # 本地状态
        ]
        
    def get_installed_plugins(self):
        """获取实际安装的插件列表"""
        installed_plugins = []
        
        if not self.plugins_path.exists():
            return installed_plugins
        
        # 检查.asar文件（实际安装的插件）
        for file in self.plugins_path.iterdir():
            if file.suffix == '.asar':
                size = file.stat().st_size
                installed_plugins.append({
                    'file': file.name,
                    'size_mb': round(size / 1024 / 1024, 2),
                    'modified': datetime.fromtimestamp(file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        return sorted(installed_plugins, key=lambda x: x['modified'], reverse=True)
    
    def analyze_user_data(self):
        """分析用户数据"""
        print("=== uTools 用户数据分析 ===\n")
        
        # 1. 基本信息
        if self.utools_path.exists():
            total_size = sum(f.stat().st_size for f in self.utools_path.rglob('*') if f.is_file())
            print(f"uTools 数据目录: {self.utools_path}")
            print(f"总占用空间: {round(total_size / 1024 / 1024, 2)} MB\n")
        else:
            print("uTools 数据目录不存在！")
            return
        
        # 2. 已安装插件
        plugins = self.get_installed_plugins()
        print(f"已安装插件数量: {len(plugins)}")
        if plugins:
            print("插件列表:")
            for i, plugin in enumerate(plugins, 1):
                print(f"  {i:2d}. {plugin['file']} ({plugin['size_mb']} MB) - {plugin['modified']}")
        print()
        
        # 3. 数据库信息
        if self.database_path.exists():
            db_dirs = [d for d in self.database_path.iterdir() if d.is_dir()]
            print(f"数据库目录数量: {len(db_dirs)}")
            for db_dir in db_dirs:
                db_size = sum(f.stat().st_size for f in db_dir.rglob('*') if f.is_file())
                print(f"  - {db_dir.name}: {round(db_size / 1024 / 1024, 2)} MB")
        print()
        
        # 4. 配置文件
        print("配置文件状态:")
        for filename in self.important_files:
            file_path = self.utools_path / filename
            if file_path.exists():
                size = file_path.stat().st_size
                modified = datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"  ✓ {filename} ({round(size / 1024, 2)} KB) - {modified}")
            else:
                print(f"  ✗ {filename} (不存在)")
    
    def create_backup(self, backup_path=None, include_plugins=True, include_database=True):
        """创建完整备份"""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = Path.cwd() / f"utools_backup_{timestamp}.zip"
        else:
            backup_path = Path(backup_path)
        
        print(f"开始创建备份到: {backup_path}")
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 1. 备份配置文件
                print("备份配置文件...")
                for filename in self.important_files:
                    file_path = self.utools_path / filename
                    if file_path.exists():
                        zipf.write(file_path, f"config/{filename}")
                        print(f"  ✓ {filename}")
                
                # 2. 备份插件
                if include_plugins and self.plugins_path.exists():
                    print("备份插件...")
                    for file in self.plugins_path.iterdir():
                        if file.is_file():
                            zipf.write(file, f"plugins/{file.name}")
                            print(f"  ✓ {file.name}")
                        elif file.is_dir() and file.name.endswith('.asar.unpacked'):
                            # 备份解压的插件文件
                            for subfile in file.rglob('*'):
                                if subfile.is_file():
                                    rel_path = subfile.relative_to(self.plugins_path)
                                    zipf.write(subfile, f"plugins/{rel_path}")
                
                # 3. 备份数据库
                if include_database and self.database_path.exists():
                    print("备份数据库...")
                    for file in self.database_path.rglob('*'):
                        if file.is_file():
                            rel_path = file.relative_to(self.database_path)
                            zipf.write(file, f"database/{rel_path}")
                
                # 4. 创建备份信息文件
                backup_info = {
                    'backup_time': datetime.now().isoformat(),
                    'utools_path': str(self.utools_path),
                    'plugins_count': len(self.get_installed_plugins()),
                    'include_plugins': include_plugins,
                    'include_database': include_database,
                    'version': '1.0'
                }
                zipf.writestr('backup_info.json', json.dumps(backup_info, indent=2, ensure_ascii=False))
            
            backup_size = backup_path.stat().st_size
            print(f"\n✓ 备份完成！")
            print(f"备份文件: {backup_path}")
            print(f"备份大小: {round(backup_size / 1024 / 1024, 2)} MB")
            
        except Exception as e:
            print(f"✗ 备份失败: {e}")
    
    def restore_backup(self, backup_file, restore_plugins=True, restore_database=True):
        """从备份恢复"""
        backup_file = Path(backup_file)
        
        if not backup_file.exists():
            print(f"备份文件不存在: {backup_file}")
            return
        
        print(f"开始从备份恢复: {backup_file}")
        
        # 安全检查：建议先关闭uTools
        print("⚠️  建议在恢复前关闭 uTools 应用！")
        confirm = input("是否继续恢复？(y/N): ").strip().lower()
        if confirm != 'y':
            print("恢复已取消")
            return
        
        try:
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # 1. 读取备份信息
                try:
                    backup_info = json.loads(zipf.read('backup_info.json').decode('utf-8'))
                    print(f"备份时间: {backup_info['backup_time']}")
                    print(f"插件数量: {backup_info['plugins_count']}")
                except:
                    print("无法读取备份信息，继续恢复...")
                
                # 2. 恢复配置文件
                print("恢复配置文件...")
                for filename in self.important_files:
                    try:
                        zipf.extract(f"config/{filename}", self.utools_path.parent)
                        # 移动到正确位置
                        src = self.utools_path.parent / "config" / filename
                        dst = self.utools_path / filename
                        if src.exists():
                            dst.parent.mkdir(parents=True, exist_ok=True)
                            shutil.move(str(src), str(dst))
                            print(f"  ✓ {filename}")
                    except KeyError:
                        print(f"  - {filename} (备份中不存在)")
                    except Exception as e:
                        print(f"  ✗ {filename} (恢复失败: {e})")
                
                # 3. 恢复插件
                if restore_plugins:
                    print("恢复插件...")
                    self.plugins_path.mkdir(parents=True, exist_ok=True)
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith('plugins/') and not file_info.is_dir():
                            try:
                                zipf.extract(file_info, self.utools_path.parent)
                                # 移动到正确位置
                                src = self.utools_path.parent / file_info.filename
                                dst = self.utools_path / file_info.filename.replace('plugins/', '', 1)
                                dst.parent.mkdir(parents=True, exist_ok=True)
                                shutil.move(str(src), str(dst))
                                print(f"  ✓ {Path(file_info.filename).name}")
                            except Exception as e:
                                print(f"  ✗ {Path(file_info.filename).name} (恢复失败: {e})")
                
                # 4. 恢复数据库
                if restore_database:
                    print("恢复数据库...")
                    self.database_path.mkdir(parents=True, exist_ok=True)
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith('database/') and not file_info.is_dir():
                            try:
                                zipf.extract(file_info, self.utools_path.parent)
                                # 移动到正确位置
                                src = self.utools_path.parent / file_info.filename
                                dst = self.utools_path / file_info.filename.replace('database/', '', 1)
                                dst.parent.mkdir(parents=True, exist_ok=True)
                                shutil.move(str(src), str(dst))
                            except Exception as e:
                                print(f"  ✗ 数据库文件恢复失败: {e}")
                
                # 清理临时目录
                temp_dirs = [self.utools_path.parent / "config", 
                           self.utools_path.parent / "plugins", 
                           self.utools_path.parent / "database"]
                for temp_dir in temp_dirs:
                    if temp_dir.exists():
                        shutil.rmtree(temp_dir, ignore_errors=True)
            
            print("\n✓ 恢复完成！")
            print("请重启 uTools 以使更改生效。")
            
        except Exception as e:
            print(f"✗ 恢复失败: {e}")

def main():
    """主函数"""
    backup_tool = UToolsBackupRestore()
    
    print("uTools 备份与恢复工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 分析用户数据")
        print("2. 创建完整备份")
        print("3. 创建配置备份（不含插件和数据库）")
        print("4. 从备份恢复")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            backup_tool.analyze_user_data()
        elif choice == '2':
            backup_tool.create_backup(include_plugins=True, include_database=True)
        elif choice == '3':
            backup_tool.create_backup(include_plugins=False, include_database=False)
        elif choice == '4':
            backup_file = input("请输入备份文件路径: ").strip()
            if backup_file:
                backup_tool.restore_backup(backup_file)
        elif choice == '5':
            print("再见!")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
