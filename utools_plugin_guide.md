# uTools 插件位置与备份恢复完整指南

## 📍 插件存储位置

### 实际安装的插件位置
```
C:\Users\<USER>\AppData\Roaming\uTools\plugins\
```

### 您的具体路径
```
C:\Users\<USER>\AppData\Roaming\uTools\plugins\
```

## 🔍 实际安装的18个插件

根据分析，您实际安装了**18个插件**，总大小约**117 MB**：

| 序号 | 文件名 | 大小 | 安装时间 |
|------|--------|------|----------|
| 1 | unsafe-59341789827b94c25e9fd7ce28dfaa88.asar | 0.95 MB | 2025-07-29 |
| 2 | 3bc319445eea914ab13f6bb5757feeb6.asar | 0.42 MB | 2025-07-24 |
| 3 | 6a529507a39d16d7d422b5ab36959d14.asar | 0.49 MB | 2025-07-23 |
| 4 | d76e374a8a76219481ce7cbe8b2327bb.asar | 6.03 MB | 2025-07-23 |
| 5 | a0cc28979d2616c52e374d422241158d.asar | 2.90 MB | 2025-07-08 |
| 6 | df3181fb49d10097e016b8f57e598e64.asar | 10.86 MB | 2025-07-03 |
| 7 | 2bf924f124f97f30773656af6f9fbc5b.asar | 0.80 MB | 2025-07-01 |
| 8 | d8e2f691aa925f4927b879800a1a1c78.asar | 0.92 MB | 2025-06-30 |
| 9 | b62cdd8936bf259f73c9fbcfc7b48bac.asar | 15.96 MB | 2025-06-24 |
| 10 | ea7b541a979b21658d031fd3a025c69f.asar | 8.23 MB | 2025-06-24 |
| 11 | 1c84e9ed6042db9169b30027cf67840e.asar | 0.94 MB | 2025-06-24 |
| 12 | 5f15a78fdd8566eacb2671acc7ffe5da.asar | 41.37 MB | 2025-06-16 |
| 13 | c5f2065ac9fa614f6d643a45b6945e66.asar | 4.93 MB | 2025-06-15 |
| 14 | e4a2a68f873161c4db91260afd5e7516.asar | 0.68 MB | 2025-06-06 |
| 15 | d5cf9b56e64361f245a3df539c972fc7.asar | 3.82 MB | 2025-06-06 |
| 16 | 0c4b8bd6cecfc7123bc038194622c1a8.asar | 1.43 MB | 2025-06-06 |
| 17 | 9441fae95f2f718928c0725a8ebef994.asar | 0.08 MB | 2025-06-06 |
| 18 | 17b3a1b8908a46f0d361afedb2c11b57.asar | 6.58 MB | 2025-06-06 |

## 📁 完整的uTools数据结构

```
C:\Users\<USER>\AppData\Roaming\uTools\
├── plugins\                    # 插件目录 (117+ MB)
│   ├── *.asar                 # 插件文件 (18个)
│   ├── *.asar.unpacked\       # 解压的插件资源
│   ├── remote                 # 插件商店信息
│   └── installs              # 安装记录
├── database\                  # 数据库目录 (5.7 MB)
│   ├── default\              # 默认数据库
│   ├── d44gvjgmwcalb2au9ocyifixq34q2fvg\
│   └── w3ifs5llrhd3nqgsf2h8vknkguruq4ay\
├── Preferences               # 用户偏好设置 (1.88 KB)
├── settings                  # 应用设置 (2.35 KB)
├── Local State              # 本地状态 (0.38 KB)
├── logs\                    # 日志文件
├── Cache\                   # 缓存目录
└── 其他配置文件...
```

## 🛠️ 提供的工具

### 1. utools_backup_restore.py - 备份恢复工具
**功能：**
- ✅ 分析用户数据和插件
- ✅ 创建完整备份（插件+配置+数据库）
- ✅ 创建配置备份（仅配置文件）
- ✅ 从备份恢复

**使用方法：**
```bash
python utools_backup_restore.py
```

### 2. plugin_name_mapper.py - 插件名称映射工具
**功能：**
- ✅ 将哈希文件名映射到实际插件名称
- ✅ 分析已安装插件详情
- ✅ 导出插件映射文件

**使用方法：**
```bash
python plugin_name_mapper.py
```

### 3. 一键启动工具 (新增)

#### utools_backup.bat - 简单启动器
**功能：**
- ✅ 一键启动备份恢复工具
- ✅ 自动检查Python环境
- ✅ 错误提示和处理

**使用方法：**
```bash
# 双击运行或命令行执行
utools_backup.bat
```

#### utools_manager.bat - 完整管理器
**功能：**
- ✅ 图形化菜单选择
- ✅ 集成所有工具
- ✅ 快速打开数据目录
- ✅ 查看使用指南

**使用方法：**
```bash
# 双击运行或命令行执行
utools_manager.bat
```

## 📦 备份与恢复操作

### 快速备份

#### 方法一：使用bat文件（推荐）
1. **一键启动**
   ```bash
   # 双击运行
   utools_manager.bat
   # 选择选项 1 -> 选择选项 2（完整备份）
   ```

2. **直接备份**
   ```bash
   # 双击运行
   utools_backup.bat
   # 选择选项 2（完整备份）
   ```

#### 方法二：使用Python命令
1. **完整备份（推荐）**
   ```bash
   python utools_backup_restore.py
   # 选择选项 2
   ```
   - 包含：插件、配置、数据库
   - 文件大小：约 130+ MB
   - 适用：系统重装、设备迁移

2. **配置备份（轻量）**
   ```bash
   python utools_backup_restore.py
   # 选择选项 3
   ```
   - 包含：仅配置文件
   - 文件大小：约 5 KB
   - 适用：设置备份

### 恢复操作
1. **关闭uTools应用**
2. **运行恢复工具**
   ```bash
   python utools_backup_restore.py
   # 选择选项 4
   # 输入备份文件路径
   ```
3. **重启uTools**

## ⚠️ 重要注意事项

### 备份前
- ✅ 确保uTools已关闭
- ✅ 检查磁盘空间（至少200MB）
- ✅ 记录当前插件列表

### 恢复前
- ⚠️ **必须关闭uTools应用**
- ⚠️ 建议备份当前配置
- ⚠️ 确认备份文件完整性

### 安全建议
- 🔄 定期备份（建议每月一次）
- 💾 多地备份（本地+云盘）
- 🧪 在测试环境先验证恢复

## 🔧 手动备份方法

如果不使用工具，也可以手动备份：

### 备份
```bash
# 1. 关闭uTools
# 2. 复制整个目录
xcopy "C:\Users\<USER>\AppData\Roaming\uTools" "D:\Backup\uTools_backup" /E /I /H
```

### 恢复
```bash
# 1. 关闭uTools
# 2. 删除原目录
rmdir "C:\Users\<USER>\AppData\Roaming\uTools" /S /Q
# 3. 恢复备份
xcopy "D:\Backup\uTools_backup" "C:\Users\<USER>\AppData\Roaming\uTools" /E /I /H
```

## 🎯 常见问题

### Q: 为什么插件文件名是哈希值？
A: uTools使用哈希值作为文件名来避免冲突和提高安全性。

### Q: 可以只备份某些插件吗？
A: 可以，但需要手动复制对应的.asar文件和相关数据库。

### Q: 恢复后插件不显示怎么办？
A: 检查是否完全关闭了uTools，然后重启应用。

### Q: 备份文件很大怎么办？
A: 可以选择仅备份配置文件，插件可以重新安装。

## 📞 技术支持

如果遇到问题，可以：
1. 检查工具输出的错误信息
2. 确认文件路径是否正确
3. 验证uTools是否完全关闭
4. 查看日志文件获取详细信息

## 🚀 快速使用

**一键启动（最简单）：**
```bash
# 双击运行管理器
utools_manager.bat
```

**立即备份：**
```bash
# 方法1：双击运行
utools_backup.bat

# 方法2：命令行
python utools_backup_restore.py
# 选择选项 2 进行完整备份
```

**分析插件：**
```bash
# 方法1：通过管理器
utools_manager.bat -> 选择选项 2

# 方法2：命令行
python plugin_name_mapper.py
# 选择选项 1 查看插件详情
```

---

**总结：您的18个插件存储在 `C:\Users\<USER>\AppData\Roaming\uTools\plugins\` 目录中，使用提供的工具可以轻松实现备份和恢复。现在您可以通过双击 `utools_manager.bat` 一键使用所有功能！**
