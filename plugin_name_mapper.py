#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uTools 插件名称映射工具
将哈希文件名映射到实际插件名称
"""

import json
import os
from pathlib import Path

class PluginNameMapper:
    def __init__(self, utools_path=None):
        if utools_path is None:
            self.utools_path = Path.home() / "AppData" / "Roaming" / "uTools"
        else:
            self.utools_path = Path(utools_path)
        
        self.plugins_path = self.utools_path / "plugins"
        self.remote_file = self.plugins_path / "remote"
    
    def load_plugin_registry(self):
        """加载插件注册表"""
        if not self.remote_file.exists():
            return None
        
        try:
            with open(self.remote_file, 'r', encoding='utf-8') as f:
                content = f.read()
                data = json.loads(content)
                plugin_data = json.loads(data['data'])
                return plugin_data.get('plugins', [])
        except Exception as e:
            print(f"读取插件注册表失败: {e}")
            return None
    
    def get_installed_plugin_files(self):
        """获取已安装的插件文件"""
        if not self.plugins_path.exists():
            return []
        
        plugin_files = []
        for file in self.plugins_path.iterdir():
            if file.suffix == '.asar':
                plugin_files.append(file.name)
        
        return plugin_files
    
    def create_name_mapping(self):
        """创建插件名称映射"""
        plugins_registry = self.load_plugin_registry()
        installed_files = self.get_installed_plugin_files()
        
        if not plugins_registry:
            print("无法加载插件注册表")
            return {}
        
        # 创建映射字典
        mapping = {}
        
        # 根据文件名模式匹配
        for plugin in plugins_registry:
            plugin_name = plugin.get('pluginName', '未知插件')
            plugin_code = plugin.get('name', '')
            
            # 查找匹配的文件
            for file_name in installed_files:
                # 移除.asar扩展名
                file_hash = file_name.replace('.asar', '')
                
                # 如果插件代码包含在文件名中，或者通过其他方式匹配
                if plugin_code and plugin_code in file_hash:
                    mapping[file_name] = {
                        'name': plugin_name,
                        'code': plugin_code,
                        'features': len(plugin.get('features', []))
                    }
                    break
        
        return mapping
    
    def analyze_installed_plugins(self):
        """分析已安装插件"""
        print("=== uTools 已安装插件分析 ===\n")
        
        # 获取文件信息
        installed_files = []
        if self.plugins_path.exists():
            for file in self.plugins_path.iterdir():
                if file.suffix == '.asar':
                    size = file.stat().st_size
                    installed_files.append({
                        'file': file.name,
                        'size_mb': round(size / 1024 / 1024, 2),
                        'hash': file.name.replace('.asar', '')
                    })
        
        # 创建名称映射
        name_mapping = self.create_name_mapping()
        
        print(f"已安装插件数量: {len(installed_files)}")
        print(f"成功映射插件: {len(name_mapping)}")
        print()
        
        # 显示映射结果
        print("插件详情:")
        for i, plugin_file in enumerate(installed_files, 1):
            file_name = plugin_file['file']
            if file_name in name_mapping:
                plugin_info = name_mapping[file_name]
                print(f"{i:2d}. {plugin_info['name']} ({plugin_file['size_mb']} MB)")
                print(f"     文件: {file_name}")
                print(f"     代码: {plugin_info['code']}")
                print(f"     功能: {plugin_info['features']}个")
            else:
                print(f"{i:2d}. 未知插件 ({plugin_file['size_mb']} MB)")
                print(f"     文件: {file_name}")
                print(f"     哈希: {plugin_file['hash']}")
            print()
        
        return name_mapping
    
    def export_mapping(self, output_file="plugin_mapping.json"):
        """导出插件映射到文件"""
        mapping = self.create_name_mapping()
        
        # 添加文件信息
        detailed_mapping = {}
        if self.plugins_path.exists():
            for file in self.plugins_path.iterdir():
                if file.suffix == '.asar':
                    file_name = file.name
                    size = file.stat().st_size
                    
                    if file_name in mapping:
                        detailed_mapping[file_name] = {
                            **mapping[file_name],
                            'size_mb': round(size / 1024 / 1024, 2),
                            'file_path': str(file)
                        }
                    else:
                        detailed_mapping[file_name] = {
                            'name': '未知插件',
                            'code': file_name.replace('.asar', ''),
                            'size_mb': round(size / 1024 / 1024, 2),
                            'file_path': str(file)
                        }
        
        # 导出到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_mapping, f, indent=2, ensure_ascii=False)
        
        print(f"插件映射已导出到: {output_file}")
        return detailed_mapping

def main():
    """主函数"""
    mapper = PluginNameMapper()
    
    print("uTools 插件名称映射工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 分析已安装插件")
        print("2. 导出插件映射")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            mapper.analyze_installed_plugins()
        elif choice == '2':
            mapper.export_mapping()
        elif choice == '3':
            print("再见!")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
