{"name": "leveldown", "version": "6.1.1", "description": "A low-level Node.js LevelDB binding", "license": "MIT", "main": "leveldown.js", "dependencies": {"abstract-leveldown": "^7.2.0", "napi-macros": "~2.0.0", "node-gyp-build": "^4.3.0"}, "devDependencies": {"async-each": "^1.0.3", "cross-env": "^7.0.3", "delayed": "^2.0.0", "dependency-check": "^4.1.0", "du": "^1.0.0", "electron": "^17.0.0", "faucet": "^0.0.1", "glob": "^7.1.3", "hallmark": "^4.0.0", "level-concat-iterator": "^3.0.0", "mkfiletree": "^2.0.0", "node-gyp": "^8.4.1", "nyc": "^15.0.0", "prebuildify": "^5.0.0", "prebuildify-ci": "^1.0.4", "prebuildify-cross": "^5.0.0", "readfiletree": "^1.0.0", "rimraf": "^3.0.0", "standard": "^16.0.3", "tape": "^5.0.1", "tempy": "^1.0.1"}, "gypfile": true, "repository": {"type": "git", "url": "https://github.com/Level/leveldown.git"}, "homepage": "https://github.com/Level/leveldown", "engines": {"node": ">=10.12.0"}}