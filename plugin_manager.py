#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
uTools 插件管理工具
用于分析和管理 uTools 插件
"""

import json
import os
from pathlib import Path
from collections import defaultdict
import hashlib

class UToolsPluginManager:
    def __init__(self, utools_path=None):
        if utools_path is None:
            self.utools_path = Path.home() / "AppData" / "Roaming" / "uTools"
        else:
            self.utools_path = Path(utools_path)
        
        self.plugins_path = self.utools_path / "plugins"
        self.remote_file = self.plugins_path / "remote"
        
    def load_plugin_data(self):
        """加载插件数据"""
        if not self.remote_file.exists():
            print(f"插件配置文件不存在: {self.remote_file}")
            return None
            
        try:
            with open(self.remote_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # 解析JSON数据
                data = json.loads(content)
                plugin_data = json.loads(data['data'])
                return plugin_data
        except Exception as e:
            print(f"读取插件数据失败: {e}")
            return None
    
    def analyze_plugins(self):
        """分析插件信息"""
        plugin_data = self.load_plugin_data()
        if not plugin_data:
            return
            
        plugins = plugin_data.get('plugins', [])
        
        # 按类别分类
        categories = defaultdict(list)
        
        for plugin in plugins:
            name = plugin.get('pluginName', '未知')
            code = plugin.get('name', '')
            platform = plugin.get('platform', 'all')
            
            # 根据插件名称和功能分类
            category = self.categorize_plugin(name, plugin.get('features', []))
            categories[category].append({
                'name': name,
                'code': code,
                'platform': platform,
                'features': len(plugin.get('features', []))
            })
        
        # 输出分析结果
        print("=== uTools 插件分析报告 ===\n")
        print(f"总插件数量: {len(plugins)}")
        print(f"插件类别数: {len(categories)}\n")
        
        for category, plugin_list in categories.items():
            print(f"【{category}】({len(plugin_list)}个)")
            for plugin in plugin_list:
                platform_info = f" [{plugin['platform']}]" if plugin['platform'] != 'all' else ""
                print(f"  - {plugin['name']}{platform_info}")
            print()
    
    def categorize_plugin(self, name, features):
        """根据插件名称和功能分类"""
        name_lower = name.lower()
        
        # AI相关
        if any(keyword in name_lower for keyword in ['ai', 'gpt', 'chatgpt', '智能']):
            return 'AI工具'
        
        # 图片处理
        elif any(keyword in name_lower for keyword in ['图片', '截图', '标图', 'image', 'photo', 'ocr', '识别', 'psd', 'png']):
            return '图片处理'
        
        # 视频音频
        elif any(keyword in name_lower for keyword in ['视频', '音频', 'video', 'audio', 'gif', 'ffmpeg']):
            return '视频音频'
        
        # 文件管理
        elif any(keyword in name_lower for keyword in ['文件', '重命名', '搜索', '浏览器', '目录', 'file']):
            return '文件管理'
        
        # 开发工具
        elif any(keyword in name_lower for keyword in ['json', '编码', '正则', 'code', '进制', 'unicode', 'base64']):
            return '开发工具'
        
        # 办公效率
        elif any(keyword in name_lower for keyword in ['计算', '发票', 'excel', '财务', '汇率', '贷款', '待办', 'todo']):
            return '办公效率'
        
        # 网络工具
        elif any(keyword in name_lower for keyword in ['下载', '网页', '网址', 'ping', '分享']):
            return '网络工具'
        
        # 系统工具
        elif any(keyword in name_lower for keyword in ['剪贴板', '进程', '颜色', '符号', '命令']):
            return '系统工具'
        
        # 通讯工具
        elif any(keyword in name_lower for keyword in ['会议', '微信', '远程', 'todesk']):
            return '通讯工具'
        
        # 安全工具
        elif any(keyword in name_lower for keyword in ['验证', '加密', '解密', '风险', '安全']):
            return '安全工具'
        
        # 娱乐工具
        elif any(keyword in name_lower for keyword in ['游戏', '五子棋', '红白机', '阅读']):
            return '娱乐工具'
        
        else:
            return '其他工具'
    
    def get_plugin_files(self):
        """获取插件文件列表"""
        if not self.plugins_path.exists():
            return []
        
        plugin_files = []
        for file in self.plugins_path.iterdir():
            if file.suffix == '.asar':
                size = file.stat().st_size
                plugin_files.append({
                    'name': file.name,
                    'size': size,
                    'size_mb': round(size / 1024 / 1024, 2)
                })
        
        return sorted(plugin_files, key=lambda x: x['size'], reverse=True)
    
    def show_storage_info(self):
        """显示存储信息"""
        plugin_files = self.get_plugin_files()
        total_size = sum(f['size'] for f in plugin_files)
        
        print("=== 插件存储信息 ===\n")
        print(f"插件文件数量: {len(plugin_files)}")
        print(f"总占用空间: {round(total_size / 1024 / 1024, 2)} MB\n")
        
        print("占用空间最大的10个插件:")
        for i, plugin in enumerate(plugin_files[:10], 1):
            print(f"{i:2d}. {plugin['name']} - {plugin['size_mb']} MB")
    
    def backup_config(self, backup_path=None):
        """备份插件配置"""
        if backup_path is None:
            backup_path = Path.cwd() / "utools_backup"
        
        backup_path = Path(backup_path)
        backup_path.mkdir(exist_ok=True)
        
        # 备份关键文件
        files_to_backup = ['remote', 'installs', 'settings', 'Preferences']
        
        for filename in files_to_backup:
            source = self.utools_path / filename
            if source.exists():
                target = backup_path / filename
                try:
                    if source.is_file():
                        target.write_bytes(source.read_bytes())
                    print(f"已备份: {filename}")
                except Exception as e:
                    print(f"备份失败 {filename}: {e}")
        
        print(f"\n配置备份完成，保存在: {backup_path}")

def main():
    """主函数"""
    manager = UToolsPluginManager()
    
    print("uTools 插件管理工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 分析插件信息")
        print("2. 查看存储信息") 
        print("3. 备份配置")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            manager.analyze_plugins()
        elif choice == '2':
            manager.show_storage_info()
        elif choice == '3':
            manager.backup_config()
        elif choice == '4':
            print("再见!")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
