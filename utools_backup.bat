@echo off
chcp 65001 >nul
title uTools 备份恢复工具

echo.
echo ========================================
echo    uTools 备份恢复工具 - 一键启动
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "utools_backup_restore.py" (
    echo [错误] 未找到 utools_backup_restore.py 文件
    echo 请确保该文件与此bat文件在同一目录下
    echo.
    pause
    exit /b 1
)

echo [信息] Python环境检查通过
echo [信息] 正在启动uTools备份恢复工具...
echo.

REM 运行Python脚本
python utools_backup_restore.py

echo.
echo [完成] 程序已退出
pause
